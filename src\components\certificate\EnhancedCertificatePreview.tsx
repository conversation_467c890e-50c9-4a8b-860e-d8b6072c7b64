/**
 * 增强的证书预览组件
 * 确保与PDF生成器完全一致的字体和布局
 */

import React, { useEffect, useState } from 'react';
import { CertificateData, CertificateTemplate } from '@/types/certificate';
import { GoogleFontsManager } from '@/lib/google-fonts-manager';

interface EnhancedCertificatePreviewProps {
  template: CertificateTemplate;
  formData: CertificateData;
  showCoordinates?: boolean;
}

export default function EnhancedCertificatePreview({
  template,
  formData,
  showCoordinates = false,
}: EnhancedCertificatePreviewProps) {
  const [fontsLoaded, setFontsLoaded] = useState(false);
  const [fontStatus, setFontStatus] = useState<Record<string, boolean>>({});

  // 预加载字体
  useEffect(() => {
    const loadFonts = async () => {
      console.log('🔤 Loading fonts for preview...');
      
      const googleFontsManager = GoogleFontsManager.getInstance();
      
      // 收集模板中使用的字体
      const fontsToLoad = [
        { family: template.layout.name.fontFamily, weight: template.layout.name.fontWeight || 600 },
        { family: template.layout.details.fontFamily, weight: template.layout.details.fontWeight || 400 },
        { family: template.layout.date.fontFamily, weight: template.layout.date.fontWeight || 400 },
        { family: template.layout.signature.fontFamily, weight: template.layout.signature.fontWeight || 400 },
      ];

      const status: Record<string, boolean> = {};
      
      for (const { family, weight } of fontsToLoad) {
        try {
          await googleFontsManager.loadFont(family, weight);
          status[`${family}-${weight}`] = true;
          console.log(`✅ Font loaded for preview: ${family} ${weight}`);
        } catch (error) {
          status[`${family}-${weight}`] = false;
          console.warn(`⚠️ Font load failed for preview: ${family} ${weight}`, error);
        }
      }

      setFontStatus(status);
      setFontsLoaded(true);
      console.log('🎉 Font loading for preview completed');
    };

    loadFonts();
  }, [template]);

  // 计算预览尺寸和缩放
  const getPreviewDimensions = () => {
    const A4_WIDTH = 595.28;
    const A4_HEIGHT = 841.89;

    let pdfWidth: number, pdfHeight: number;
    let containerMaxWidth: number;

    if (template.orientation === 'landscape') {
      pdfWidth = A4_HEIGHT;  // 841.89
      pdfHeight = A4_WIDTH;  // 595.28
      containerMaxWidth = 800; // max-w-2xl
    } else {
      pdfWidth = A4_WIDTH;   // 595.28
      pdfHeight = A4_HEIGHT; // 841.89
      containerMaxWidth = 448; // max-w-md
    }

    const scaleFactor = containerMaxWidth / pdfWidth;
    const displayWidth = containerMaxWidth;
    const displayHeight = pdfHeight * scaleFactor;

    return {
      pdfWidth,
      pdfHeight,
      scaleFactor,
      displayWidth,
      displayHeight,
    };
  };

  const { pdfWidth, pdfHeight, scaleFactor, displayWidth, displayHeight } = getPreviewDimensions();

  // 获取字体样式
  const getFontStyle = (family: string, fontSize: number, fontWeight: number | string = 400) => {
    // 映射到CSS字体族
    const fontFamilyMap: Record<string, string> = {
      'Dancing Script': 'var(--font-dancing-script), "Dancing Script", cursive',
      'Playfair Display': 'var(--font-playfair-display), "Playfair Display", serif',
      'Inter': 'var(--font-inter), "Inter", sans-serif',
      'Crimson Text': 'var(--font-crimson-text), "Crimson Text", serif',
      'Source Sans Pro': 'var(--font-source-sans-pro), "Source Sans Pro", sans-serif',
      'Great Vibes': 'var(--font-great-vibes), "Great Vibes", cursive',
    };

    return {
      fontFamily: fontFamilyMap[family] || 'Arial, sans-serif',
      fontSize: `${fontSize * scaleFactor}px`,
      fontWeight: fontWeight,
      lineHeight: '1.0',
      textRendering: 'optimizeLegibility' as const,
      WebkitFontSmoothing: 'antialiased' as const,
      MozOsxFontSmoothing: 'grayscale' as const,
    };
  };

  // 计算字段位置
  const getFieldPosition = (field: any) => {
    return {
      left: `${field.x * scaleFactor}px`,
      top: `${(pdfHeight - field.y - field.height) * scaleFactor}px`,
      width: `${field.width * scaleFactor}px`,
      height: `${field.height * scaleFactor}px`,
    };
  };

  // 获取对齐样式
  const getAlignmentStyle = (align: 'left' | 'center' | 'right') => {
    return {
      display: 'flex',
      alignItems: 'center',
      justifyContent: align === 'center' ? 'center' : align === 'right' ? 'flex-end' : 'flex-start',
      textAlign: align as any,
    };
  };

  const previewContainerClass = template.orientation === 'landscape'
    ? 'aspect-[4/3] w-full max-w-2xl mx-auto'
    : 'aspect-[3/4] w-full max-w-md mx-auto';

  return (
    <div className="space-y-4">
      {/* 字体加载状态 */}
      {!fontsLoaded && (
        <div className="text-center text-sm text-gray-500">
          🔤 Loading fonts...
        </div>
      )}

      {/* 字体状态调试信息 */}
      {showCoordinates && fontsLoaded && (
        <div className="text-xs bg-gray-100 p-2 rounded">
          <div className="font-semibold mb-1">Font Status:</div>
          {Object.entries(fontStatus).map(([key, loaded]) => (
            <div key={key} className={loaded ? 'text-green-600' : 'text-red-600'}>
              {loaded ? '✅' : '❌'} {key}
            </div>
          ))}
        </div>
      )}

      {/* 预览容器 */}
      <div className={previewContainerClass}>
        <div
          className="relative w-full h-full border border-gray-300 shadow-lg bg-white overflow-hidden"
          style={{
            width: `${displayWidth}px`,
            height: `${displayHeight}px`,
          }}
        >
          {/* 背景 */}
          {template.background?.color && template.background.color !== '#ffffff' && (
            <div
              className="absolute inset-0"
              style={{ backgroundColor: template.background.color }}
            />
          )}

          {/* 边框 */}
          {template.border?.width > 0 && (
            <div
              className="absolute inset-0"
              style={{
                border: `${template.border.width * scaleFactor}px solid ${template.border.color}`,
              }}
            />
          )}

          {/* 装饰线条 */}
          {template.decorations?.lines?.map((line, index) => (
            <div
              key={`line-${index}`}
              className="absolute"
              style={{
                left: `${line.x1 * scaleFactor}px`,
                top: `${(pdfHeight - line.y1) * scaleFactor}px`,
                width: `${Math.sqrt(Math.pow(line.x2 - line.x1, 2) + Math.pow(line.y2 - line.y1, 2)) * scaleFactor}px`,
                height: `${line.thickness * scaleFactor}px`,
                backgroundColor: line.color,
                transformOrigin: 'left center',
                transform: `rotate(${Math.atan2(line.y1 - line.y2, line.x2 - line.x1)}rad)`,
              }}
            />
          ))}

          {/* 收件人姓名 */}
          {formData.recipientName && (
            <div
              className="absolute"
              style={{
                ...getFieldPosition(template.layout.name),
                color: template.layout.name.color,
                ...getFontStyle(
                  template.layout.name.fontFamily,
                  template.layout.name.fontSize,
                  template.layout.name.fontWeight || 600
                ),
                ...getAlignmentStyle(template.layout.name.align),
              }}
            >
              {formData.recipientName}
            </div>
          )}

          {/* 详细信息 */}
          {formData.details && (
            <div
              className="absolute"
              style={{
                ...getFieldPosition(template.layout.details),
                color: template.layout.details.color,
                ...getFontStyle(
                  template.layout.details.fontFamily,
                  template.layout.details.fontSize,
                  template.layout.details.fontWeight || 400
                ),
                ...getAlignmentStyle(template.layout.details.align),
                lineHeight: '1.4',
              }}
            >
              {formData.details}
            </div>
          )}

          {/* 日期 */}
          {formData.date && (
            <div
              className="absolute"
              style={{
                ...getFieldPosition(template.layout.date),
                color: template.layout.date.color,
                ...getFontStyle(
                  template.layout.date.fontFamily,
                  template.layout.date.fontSize,
                  template.layout.date.fontWeight || 400
                ),
                ...getAlignmentStyle(template.layout.date.align),
              }}
            >
              {formData.date}
            </div>
          )}

          {/* 签名 */}
          {formData.signature && (
            <div
              className="absolute"
              style={{
                ...getFieldPosition(template.layout.signature),
                color: template.layout.signature.color,
                ...getFontStyle(
                  template.layout.signature.fontFamily,
                  template.layout.signature.fontSize,
                  template.layout.signature.fontWeight || 400
                ),
                ...getAlignmentStyle(template.layout.signature.align),
                letterSpacing: template.layout.signature.fontFamily.includes('Dancing') ||
                              template.layout.signature.fontFamily.includes('Great Vibes') ? '0.02em' : 'normal',
              }}
            >
              {formData.signature}
            </div>
          )}

          {/* 坐标调试 */}
          {showCoordinates && (
            <>
              {/* 姓名区域 */}
              <div
                className="absolute border-2 border-red-500 bg-red-100 bg-opacity-30"
                style={getFieldPosition(template.layout.name)}
              >
                <span className="text-xs text-red-700 font-mono">Name</span>
              </div>

              {/* 详情区域 */}
              <div
                className="absolute border-2 border-blue-500 bg-blue-100 bg-opacity-30"
                style={getFieldPosition(template.layout.details)}
              >
                <span className="text-xs text-blue-700 font-mono">Details</span>
              </div>

              {/* 日期区域 */}
              <div
                className="absolute border-2 border-green-500 bg-green-100 bg-opacity-30"
                style={getFieldPosition(template.layout.date)}
              >
                <span className="text-xs text-green-700 font-mono">Date</span>
              </div>

              {/* 签名区域 */}
              <div
                className="absolute border-2 border-purple-500 bg-purple-100 bg-opacity-30"
                style={getFieldPosition(template.layout.signature)}
              >
                <span className="text-xs text-purple-700 font-mono">Signature</span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* 预览信息 */}
      <div className="text-xs text-gray-500 text-center">
        预览尺寸: {Math.round(displayWidth)} x {Math.round(displayHeight)}px 
        (缩放: {Math.round(scaleFactor * 100)}%)
      </div>
    </div>
  );
}
