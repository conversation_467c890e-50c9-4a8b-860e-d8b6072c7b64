'use client';

import React, { useState, useCallback } from 'react';

import { Button } from '@/components/ui/button';

import SimpleCertificateForm from './SimpleCertificateForm';

import { CertificateData, CertificateTemplate } from '@/types/certificate';
import { getDefaultTemplate } from '@/lib/certificate-templates';
import { Download, Loader2, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { certificateAnalytics } from '@/lib/analytics';
import { generateCertificatePDF } from '@/lib/pdf-generator';
import { Progress } from '@/components/ui/progress';
import { SlideIn, ScaleIn } from '@/components/ui/animations';

interface CertificateMakerProps {
  selectedTemplate?: CertificateTemplate;
  templates?: CertificateTemplate[];
  onTemplateChange?: (template: CertificateTemplate) => void;
}

export default function CertificateMaker({
  selectedTemplate: initialTemplate,
  templates = [],
  onTemplateChange
}: CertificateMakerProps) {
  const { toast } = useToast();
  // 直接使用传入的模板，不再支持模板切换
  const selectedTemplate = initialTemplate || getDefaultTemplate();

  const [formData, setFormData] = useState<CertificateData>({
    templateId: getDefaultTemplate().id,
    recipientName: '',
    date: '',
    signature: '',
    details: '',
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [progressStatus, setProgressStatus] = useState('');



  const handleFormDataChange = useCallback((data: Partial<CertificateData>) => {
    setFormData(prev => ({
      ...prev,
      ...data,
    }));
  }, []);

  const handleGeneratePDF = useCallback(async () => {
    if (!selectedTemplate || !formData.recipientName || !formData.date || !formData.signature || !formData.details) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields before generating the certificate.",
        variant: "destructive",
      });

      // 跟踪错误
      certificateAnalytics.errorOccurred('validation_error', 'Missing required fields', 'pdf_generation');
      return;
    }

    // 跟踪生成开始
    certificateAnalytics.certificateGenerationStarted(selectedTemplate.id, formData);

    const startTime = Date.now();
    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      // 阶段1: 初始化 (0-15%)
      setProgressStatus('Initializing certificate generation...');
      setGenerationProgress(5);
      await new Promise(resolve => setTimeout(resolve, 100));

      setProgressStatus('Preparing template...');
      setGenerationProgress(15);
      await new Promise(resolve => setTimeout(resolve, 100));

      // 阶段2: 字体加载 (15-40%)
      setProgressStatus('Loading fonts...');
      setGenerationProgress(25);
      await new Promise(resolve => setTimeout(resolve, 150));

      setProgressStatus('Optimizing font rendering...');
      setGenerationProgress(40);
      await new Promise(resolve => setTimeout(resolve, 100));

      // 阶段3: PDF文档创建 (40-60%)
      setProgressStatus('Creating PDF document...');
      setGenerationProgress(50);
      await new Promise(resolve => setTimeout(resolve, 100));

      setProgressStatus('Setting up page layout...');
      setGenerationProgress(60);

      // 阶段4: 内容渲染 (60-85%)
      setProgressStatus('Rendering certificate content...');
      setGenerationProgress(70);
      await new Promise(resolve => setTimeout(resolve, 100));

      // 实际PDF生成
      setProgressStatus('Generating PDF...');
      await generateCertificatePDF(selectedTemplate, formData);

      setProgressStatus('Finalizing document...');
      setGenerationProgress(85);
      await new Promise(resolve => setTimeout(resolve, 100));

      // 阶段5: 完成 (85-100%)
      setProgressStatus('Preparing download...');
      setGenerationProgress(95);
      await new Promise(resolve => setTimeout(resolve, 100));

      setProgressStatus('Complete!');
      setGenerationProgress(100);
      await new Promise(resolve => setTimeout(resolve, 200));

      const endTime = Date.now();
      const generationTime = endTime - startTime;

      // 跟踪生成完成
      certificateAnalytics.certificateGenerated(selectedTemplate.id, generationTime);

      toast({
        title: "Certificate Generated!",
        description: "Your certificate has been generated and downloaded successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error generating PDF:', error);

      // 跟踪错误
      certificateAnalytics.errorOccurred('generation_error', error instanceof Error ? error.message : 'Unknown error', 'pdf_generation');

      toast({
        title: "Generation Failed",
        description: "There was an error generating your certificate. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
      setProgressStatus('');
    }
  }, [selectedTemplate, formData, toast]);

  const isFormValid = formData.recipientName && formData.date && formData.signature && formData.details;

  return (
    <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* 单列布局 - 参考 Bannerbear 设计 */}
      <div className="space-y-6 sm:space-y-8">


        {/* 表单和预览 - 统一的左右布局 */}
        <div className="space-y-6 sm:space-y-8">
          {/* 竖向模板的模板选择器 */}
          {selectedTemplate.orientation === 'portrait' && templates.length > 1 && (
            <SlideIn direction="up" delay={100}>
              <div className="text-center">
                {/* <h3 className="text-2xl font-bold text-gray-900 mb-6">Choose Your Template</h3> */}
                {(() => {
                  const shouldShowCarousel = templates.length > 5;

                  if (!shouldShowCarousel) {
                    // 直接展示所有纵向模板（1-5个）
                    return (
                      <div className="flex justify-center">
                        <div className={`grid gap-3 sm:gap-4 ${
                          templates.length === 1 ? 'grid-cols-1 max-w-xs' :
                          templates.length === 2 ? 'grid-cols-2 max-w-2xl' :
                          templates.length === 3 ? 'grid-cols-2 sm:grid-cols-3 max-w-3xl' :
                          templates.length === 4 ? 'grid-cols-2 sm:grid-cols-4 max-w-4xl' :
                          'grid-cols-2 sm:grid-cols-3 md:grid-cols-5 max-w-5xl'
                        }`}>
                          {templates.map((template) => (
                            <div
                              key={template.id}
                              className={`relative cursor-pointer rounded-lg border-2 transition-all duration-200 ${
                                selectedTemplate?.id === template.id
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-200 hover:border-gray-300'
                              } w-full max-w-xs touch-manipulation`}
                              onClick={() => onTemplateChange?.(template)}
                            >
                              <div className="p-3">
                                <div className="bg-gray-50 rounded-lg overflow-hidden aspect-[3/4] mb-3">
                                  <img
                                    src={template.preview}
                                    alt={template.displayName}
                                    className="w-full h-full object-contain p-1"
                                    draggable={false}
                                  />
                                </div>
                                {selectedTemplate?.id === template.id && (
                                  <div className="absolute top-2 right-2">
                                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                                      <CheckCircle className="w-3 h-3 text-white" />
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  } else {
                    // 使用轮播组件（超过5个模板时）
                    const currentIndex = templates.findIndex(t => t.id === selectedTemplate?.id);
                    const templatesPerPage = 5; // 固定每页显示5个模板
                    const currentPage = Math.floor(currentIndex / templatesPerPage);
                    const totalPages = Math.ceil(templates.length / templatesPerPage);
                    const startIndex = currentPage * templatesPerPage;
                    const endIndex = Math.min(startIndex + templatesPerPage, templates.length);
                    const visibleTemplates = templates.slice(startIndex, endIndex);

                    const canGoPrev = currentPage > 0;
                    const canGoNext = currentPage < totalPages - 1;

                    const goToPrevPage = () => {
                      if (canGoPrev) {
                        const newIndex = (currentPage - 1) * templatesPerPage;
                        onTemplateChange?.(templates[newIndex]);
                      }
                    };

                    const goToNextPage = () => {
                      if (canGoNext) {
                        const newIndex = (currentPage + 1) * templatesPerPage;
                        onTemplateChange?.(templates[newIndex]);
                      }
                    };

                    return (
                      <div className="relative max-w-8xl mx-auto">

                        <div className="flex items-center justify-center space-x-4">
                          {/* 左导航按钮 */}
                          <button
                            onClick={goToPrevPage}
                            disabled={!canGoPrev}
                            className={`flex-shrink-0 p-2 sm:p-3 rounded-full shadow-md transition-all border min-h-[44px] min-w-[44px] flex items-center justify-center ${
                              canGoPrev
                                ? 'bg-white hover:shadow-lg border-gray-200 hover:border-gray-300 text-gray-600 hover:text-gray-800'
                                : 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                            }`}
                            aria-label="Previous page"
                          >
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                          </button>

                          {/* 模板网格 - 与非轮播模式完全一致的布局和样式 */}
                          <div className="flex-1 overflow-hidden">
                            <div className="flex justify-center">
                              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3 sm:gap-4 max-w-5xl">
                                {visibleTemplates.map((template) => (
                                  <div
                                    key={template.id}
                                    className={`relative cursor-pointer rounded-lg border-2 transition-all duration-200 ${
                                      selectedTemplate?.id === template.id
                                        ? 'border-blue-500 bg-blue-50'
                                        : 'border-gray-200 hover:border-gray-300'
                                    } w-full max-w-xs touch-manipulation`}
                                    onClick={() => onTemplateChange?.(template)}
                                  >
                                    <div className="p-3">
                                      <div className="bg-gray-50 rounded-lg overflow-hidden aspect-[3/4] mb-3">
                                        <img
                                          src={template.preview}
                                          alt={template.displayName}
                                          className="w-full h-full object-contain p-1"
                                          draggable={false}
                                        />
                                      </div>
                                      {selectedTemplate?.id === template.id && (
                                        <div className="absolute top-2 right-2">
                                          <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                                            <CheckCircle className="w-3 h-3 text-white" />
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>

                          {/* 右导航按钮 */}
                          <button
                            onClick={goToNextPage}
                            disabled={!canGoNext}
                            className={`flex-shrink-0 p-2 sm:p-3 rounded-full shadow-md transition-all border min-h-[44px] min-w-[44px] flex items-center justify-center ${
                              canGoNext
                                ? 'bg-white hover:shadow-lg border-gray-200 hover:border-gray-300 text-gray-600 hover:text-gray-800'
                                : 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                            }`}
                            aria-label="Next page"
                          >
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </button>
                        </div>

                        {/* 页面指示器 */}
                        <div className="mt-6 text-center">
                          {totalPages > 1 && (
                            <div className="flex justify-center items-center space-x-2">
                              {/* <span className="text-sm text-gray-600">
                                Page {currentPage + 1} of {totalPages}
                              </span> */}
                              <div className="flex space-x-1">
                                {Array.from({ length: totalPages }, (_, i) => (
                                  <button
                                    key={i}
                                    onClick={() => {
                                      const newIndex = i * templatesPerPage;
                                      onTemplateChange?.(templates[newIndex]);
                                    }}
                                    className={`w-2 h-2 rounded-full transition-all duration-200 ${
                                      i === currentPage
                                        ? 'bg-blue-500 scale-125'
                                        : 'bg-gray-300 hover:bg-gray-400'
                                    }`}
                                    aria-label={`Go to page ${i + 1}`}
                                  />
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  }
                })()}
              </div>
            </SlideIn>
          )}

          {/* 居中的表单布局 */}
          <div className="flex justify-center">
            <div className="w-full max-w-2xl">
              <SlideIn direction="up" delay={200}>
                <div className="bg-white rounded-lg shadow-lg p-6 sm:p-8 space-y-6">
                  {/* 表单标题 */}
                  <div className="text-center">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">
                      Create Your Certificate
                    </h2>
                    <p className="text-gray-600">
                      Fill in the details below to generate your professional certificate
                    </p>
                  </div>

                  {/* 表单内容 */}
                  <SimpleCertificateForm
                    template={selectedTemplate}
                    formData={formData}
                    onFormDataChange={handleFormDataChange}
                  />

                  {/* 生成按钮区域 */}
                  <div className="space-y-4 pt-4 border-t border-gray-200">
                    <Button
                      onClick={handleGeneratePDF}
                      disabled={!isFormValid || isGenerating}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white py-4 text-lg font-medium transition-all duration-200 hover:shadow-lg min-h-[56px] touch-manipulation"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="mr-3 h-6 w-6 animate-spin" />
                          {generationProgress < 100 ? `Generating... ${generationProgress}%` : 'Complete!'}
                        </>
                      ) : (
                        <>
                          <Download className="mr-3 h-6 w-6" />
                          Generate Certificate PDF
                        </>
                      )}
                    </Button>

                    {/* 进度条 */}
                    {isGenerating && (
                      <ScaleIn>
                        <div className="space-y-3">
                          <Progress value={generationProgress} className="w-full h-3" />
                          <div className="text-center">
                            <p className="text-sm text-gray-700 font-medium">
                              {progressStatus}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              {generationProgress}% complete
                            </p>
                          </div>
                        </div>
                      </ScaleIn>
                    )}

                    {/* 表单验证提示 */}
                    {!isFormValid && (
                      <p className="text-sm text-center text-gray-500">
                        Please fill in all required fields to generate your certificate
                      </p>
                    )}
                  </div>
                </div>
              </SlideIn>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
}
