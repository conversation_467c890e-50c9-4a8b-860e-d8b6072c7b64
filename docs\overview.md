# 项目概述

Certificate Maker 是一个现代化的证书生成器，专为解决 Dancing Script 等手写字体在 PDF 生成中的显示问题而设计。

## 🎯 项目目标

- 提供高质量的证书生成服务
- 解决手写字体在 PDF 中的显示问题
- 提供优秀的用户体验和性能
- 建立可扩展的字体管理架构

## 🏗️ 技术架构

### 前端架构
```
Next.js 14 (App Router)
├── React 18
├── TypeScript
├── Tailwind CSS
└── Shadcn/ui
```

### PDF 生成架构
```
客户端预览 + 服务器端生成
├── pdf-lib (PDF 生成)
├── fontkit (字体处理)
├── 统一字体嵌入器
└── 智能缓存系统
```

### 字体管理架构
```
UnifiedFontEmbedder (统一字体嵌入器)
├── FontCacheManager (缓存管理)
├── FontFallbackManager (降级管理)
└── DancingScriptTestSuite (测试套件)
```

## 🌟 核心特性

### 1. 智能字体管理
- **统一字体嵌入器**: 整合多种字体加载策略
- **智能缓存**: 减少重复下载，提升性能
- **多层次降级**: 确保字体始终可用
- **完整权重支持**: Regular、Medium、SemiBold、Bold

### 2. 高质量 PDF 生成
- **客户端预览**: 快速响应，实时预览
- **服务器端生成**: 高质量输出，字体完美嵌入
- **混合架构**: 兼顾速度和质量
- **字体子集化**: 优化文件大小

### 3. 优雅错误处理
- **多层次降级策略**: 确保系统稳定性
- **详细错误日志**: 便于问题诊断
- **自动恢复机制**: 最小化用户影响
- **优雅降级**: 保证基本功能可用

### 4. 性能优化
- **字体预加载**: 提升首次使用体验
- **智能缓存**: 内存 + 持久化存储
- **批量操作**: 减少网络请求
- **性能监控**: 实时性能指标

## 📊 系统组件

### 核心库 (src/lib/)

#### 字体管理
- `unified-font-embedder.ts` - 统一字体嵌入器
- `font-cache-manager.ts` - 字体缓存管理器
- `font-fallback-manager.ts` - 字体降级管理器
- `fonts.ts` - 字体配置和映射

#### PDF 生成
- `pdf-generator.ts` - PDF 生成器
- `server-pdf-client.ts` - 服务器端 PDF 客户端

#### 测试和质量保证
- `dancing-script-test-suite.ts` - 完整测试套件
- `preview-pdf-consistency-test.ts` - 预览一致性测试

#### 模板和配置
- `template-manager.ts` - 模板管理器
- `certificate-templates.ts` - 证书模板定义

### API 路由 (src/app/api/)
- `generate-pdf/route.ts` - 服务器端 PDF 生成 API

### 测试页面 (src/app/)
- `tests/` - 测试中心
- `test-dancing-script-improved/` - 改进版字体测试
- `test-dancing-script-suite/` - 完整测试套件
- `test-server-pdf/` - 服务器端 PDF 测试
- `test-font-performance/` - 性能测试
- `test-font-fallback/` - 降级测试

## 🎨 字体支持

### Dancing Script (主要解决目标)
```
权重支持: 400, 500, 600, 700
文件路径: /fonts/Dancing_Script/
降级策略: Dancing Script → Times Roman Italic → Helvetica
```

### 其他支持字体
- **Great Vibes**: 手写风格字体
- **Playfair Display**: 优雅衬线字体
- **Inter**: 现代无衬线字体
- **Crimson Text**: 经典衬线字体
- **Source Sans 3**: 专业无衬线字体

## 🔄 字体加载流程

```mermaid
graph TD
    A[请求字体] --> B{检查缓存}
    B -->|命中| C[返回缓存字体]
    B -->|未命中| D[尝试本地字体]
    D -->|成功| E[嵌入字体]
    D -->|失败| F[尝试Google Fonts]
    F -->|成功| E
    F -->|失败| G[使用降级策略]
    G --> H[标准字体后备]
    E --> I[缓存字体]
    I --> J[返回结果]
    C --> J
    H --> J
```

## 📈 性能指标

### 字体加载性能
- **首次加载**: < 5秒
- **缓存加载**: < 100ms
- **缓存命中率**: > 80%

### PDF 生成性能
- **客户端生成**: 2-5秒
- **服务器端生成**: 1-3秒
- **文件大小**: 通常 < 500KB

### 系统可靠性
- **字体加载成功率**: > 99%
- **PDF 生成成功率**: > 99.5%
- **降级策略覆盖率**: 100%

## 🧪 测试覆盖

### 测试类别
1. **基础字体加载测试** - 验证字体正确加载
2. **字体权重测试** - 测试所有权重变体
3. **PDF 嵌入测试** - 验证字体正确嵌入
4. **模板兼容性测试** - 检查模板配置
5. **性能测试** - 测量加载时间和缓存效果
6. **渲染质量测试** - 验证字体显示质量

### 测试工具
- **自动化测试套件**: 全面的功能测试
- **性能基准测试**: 性能指标监控
- **兼容性测试**: 跨浏览器测试
- **压力测试**: 高负载场景测试

## 🔧 配置管理

### 字体配置
```typescript
// 本地字体路径配置
const LOCAL_FONTS = {
  'Dancing Script': {
    400: '/fonts/Dancing_Script/DancingScript-Regular.ttf',
    500: '/fonts/Dancing_Script/DancingScript-Medium.ttf',
    600: '/fonts/Dancing_Script/DancingScript-SemiBold.ttf',
    700: '/fonts/Dancing_Script/DancingScript-Bold.ttf'
  }
};
```

### 缓存配置
```typescript
// 缓存设置
const CACHE_CONFIG = {
  maxSize: 50 * 1024 * 1024, // 50MB
  maxAge: 24 * 60 * 60 * 1000, // 24小时
  cleanupInterval: 60 * 60 * 1000 // 1小时
};
```

### 降级配置
```typescript
// 降级策略配置
const FALLBACK_STRATEGIES = {
  'Dancing Script': [
    { type: 'local', family: 'Dancing Script' },
    { type: 'google', family: 'Dancing Script' },
    { type: 'standard', font: 'TimesRomanItalic' }
  ]
};
```

## 🚀 部署架构

### 开发环境
- **本地开发**: Next.js 开发服务器
- **热重载**: 实时代码更新
- **调试工具**: 详细的日志和测试页面

### 生产环境
- **平台**: Vercel (推荐) 或任何 Node.js 平台
- **CDN**: 静态资源加速
- **缓存策略**: 多层缓存优化
- **监控**: 性能和错误监控

## 📚 文档体系

### 用户文档
- **README.md** - 项目介绍和快速开始
- **使用指南** - 详细的使用说明
- **故障排除** - 常见问题解决方案

### 开发文档
- **API 文档** - 详细的 API 说明
- **架构文档** - 系统架构和设计
- **贡献指南** - 开发和贡献流程

### 技术文档
- **字体解决方案** - Dancing Script 问题解决方案
- **性能优化** - 性能优化策略
- **测试指南** - 测试方法和工具

## 🔮 未来规划

### 短期目标 (1-3个月)
- 支持更多手写字体
- 优化移动端体验
- 增强错误处理

### 中期目标 (3-6个月)
- 多语言支持
- 模板编辑器
- 批量生成功能

### 长期目标 (6-12个月)
- 云端字体库
- AI 辅助设计
- 企业级功能

## 🤝 贡献方式

### 开发贡献
- 代码贡献
- 功能建议
- Bug 报告

### 文档贡献
- 文档改进
- 翻译工作
- 使用案例

### 社区贡献
- 问题解答
- 经验分享
- 推广宣传

---

Certificate Maker 致力于提供最佳的证书生成体验，特别是在字体处理方面的技术创新。我们欢迎社区的参与和贡献，共同打造更好的产品。
