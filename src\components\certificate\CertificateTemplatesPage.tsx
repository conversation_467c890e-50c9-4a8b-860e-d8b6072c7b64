'use client';

import Link from 'next/link';
import { CertificateCategory } from '@/types/certificate';
import { TemplateManager } from '@/lib/template-manager';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import BreadcrumbNavigation from './BreadcrumbNavigation';
import CategoryPreviewGrid from './CategoryPreviewGrid';
import { Award, BookOpen, Users, Star, ArrowRight, TrendingUp, Download, Zap } from 'lucide-react';

export default function CertificateTemplatesPage() {
  // 获取所有分类数据
  const categories = TemplateManager.getAllCategories();



  return (
    <div className="container mx-auto px-4 py-8">
      {/* 面包屑导航 */}
      <BreadcrumbNavigation 
        items={[
          { name: 'Home', url: '/' },
          { name: 'Certificate Templates', url: '/certificate-templates/', current: true }
        ]}
      />

      {/* 页面头部 */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Professional Certificate Templates
        </h1>
        <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-6">
          Create stunning, professional certificates with our free online templates. Choose from our carefully designed collection and customize with your details in minutes.
        </p>
        
        {/* 统计信息 */}
        <div className="flex justify-center items-center space-x-6 text-sm text-gray-500">
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">{categories.length} Categories</Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">
              {categories.reduce((total, cat) => total + (cat.templateCount || 0), 0)} Templates
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">100% Free</Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">Instant Download</Badge>
          </div>
        </div>
      </div>

      {/* 分类网格 */}
      <div className="mb-16">
        <CategoryPreviewGrid
          categories={categories}
          title=""
          subtitle=""
          className=""
        />
      </div>

      {/* 特性展示 */}
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">
          Why Choose Our Certificate Templates?
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
          <Card className="text-center border-2 hover:border-blue-200 transition-colors">
            <CardHeader>
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-blue-600" />
              </div>
              <CardTitle className="text-xl">Instant Creation</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-base">
                Create professional certificates in under 2 minutes with our intuitive interface.
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center border-2 hover:border-green-200 transition-colors">
            <CardHeader>
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Download className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-xl">High-Quality PDF</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-base">
                Download print-ready PDFs with crisp text and professional formatting.
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center border-2 hover:border-purple-200 transition-colors">
            <CardHeader>
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-8 h-8 text-purple-600" />
              </div>
              <CardTitle className="text-xl">Professional Quality</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-base">
                Templates designed by professionals for maximum impact and credibility.
              </CardDescription>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* SEO优化内容部分 */}
      <div className="space-y-16">
        {/* 关于我们的证书模板 */}
        <section className="bg-gray-50 rounded-2xl p-8">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">
              Professional Certificate Templates for Every Need
            </h2>
            <div className="prose prose-lg mx-auto text-gray-700">
              <p className="text-xl leading-relaxed mb-6">
                Our comprehensive collection of <strong>free certificate templates</strong> provides everything you need to create professional,
                high-quality certificates for any occasion. Whether you're recognizing achievements, documenting course completion,
                acknowledging participation, or celebrating excellence, our templates are designed to meet the highest standards of
                professional presentation.
              </p>
              <p className="text-lg leading-relaxed mb-6">
                Each template in our collection has been carefully crafted by professional designers to ensure maximum visual impact
                and credibility. From <strong>achievement certificates</strong> that celebrate success to <strong>completion certificates</strong> that
                document learning milestones, our templates serve educational institutions, corporations, non-profit organizations,
                and individuals worldwide.
              </p>
              <p className="text-lg leading-relaxed">
                With our user-friendly online certificate maker, you can customize any template with your specific details,
                preview your certificate in real-time, and download a high-resolution PDF ready for printing or digital sharing.
                No design experience required – just select your template, fill in the details, and create professional certificates in minutes.
              </p>
            </div>
          </div>
        </section>

        {/* 使用场景 */}
        <section>
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Perfect for Organizations and Individuals
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Educational Institutions</h3>
              <p className="text-gray-600">
                Schools, universities, and training centers use our templates for course completion, academic achievement,
                and graduation certificates.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Corporate Organizations</h3>
              <p className="text-gray-600">
                Businesses recognize employee achievements, training completion, and professional development milestones
                with our professional templates.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Event Organizers</h3>
              <p className="text-gray-600">
                Conference organizers, workshop facilitators, and seminar hosts create participation certificates
                to acknowledge attendee engagement.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-yellow-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Individual Users</h3>
              <p className="text-gray-600">
                Personal trainers, coaches, and individual instructors create certificates for their clients
                and students to recognize progress and achievements.
              </p>
            </div>
          </div>
        </section>

        {/* 如何使用 */}
        <section className="bg-blue-50 rounded-2xl p-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">
              How to Create Your Certificate in 3 Simple Steps
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
              <div>
                <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  1
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Choose Your Template</h3>
                <p className="text-gray-600">
                  Browse our collection and select the perfect template for your needs. Each category offers multiple
                  professional designs to choose from.
                </p>
              </div>
              <div>
                <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  2
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Customize Your Details</h3>
                <p className="text-gray-600">
                  Fill in the recipient's name, achievement details, date, and signature. See your changes reflected
                  in real-time with our live preview feature.
                </p>
              </div>
              <div>
                <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  3
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Download & Share</h3>
                <p className="text-gray-600">
                  Generate your high-quality PDF certificate instantly. Print it on premium paper or share it digitally
                  with recipients and stakeholders.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* 开始创建 */}
        <section className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">
            Ready to Create Professional Certificates?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-4xl mx-auto">
            Join thousands of organizations and individuals who trust our certificate templates for their recognition needs.
            Start creating professional certificates today – it's completely free!
          </p>
          <div className="flex justify-center items-center space-x-8 text-sm text-gray-600 mb-8">
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">100% Free</Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">No Registration</Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">Instant Download</Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">Professional Quality</Badge>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
