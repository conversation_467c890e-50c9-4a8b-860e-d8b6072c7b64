'use client';

import { useState, useEffect } from 'react';
import { PDFDocument } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { UnifiedFontEmbedder } from '@/lib/unified-font-embedder';
import { FontCacheManager } from '@/lib/font-cache-manager';

export default function TestFontPerformancePage() {
  const [logs, setLogs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [cacheStats, setCacheStats] = useState<any>(null);

  const addLog = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const updateCacheStats = () => {
    const cacheManager = FontCacheManager.getInstance();
    const stats = cacheManager.getStats();
    setCacheStats(stats);
  };

  useEffect(() => {
    updateCacheStats();
  }, []);

  const testFontPreloading = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🚀 开始字体预加载测试...');
      
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);
      
      addLog('📦 开始预加载项目字体...');
      const preloadStart = performance.now();
      
      await fontEmbedder.preloadProjectFonts();
      
      const preloadTime = performance.now() - preloadStart;
      addLog(`✅ 字体预加载完成 (${Math.round(preloadTime)}ms)`);
      
      updateCacheStats();
      
    } catch (error) {
      addLog(`❌ 预加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testCachePerformance = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🚀 开始缓存性能测试...');
      
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);
      
      // 测试首次加载（无缓存）
      addLog('🔄 测试首次加载（无缓存）...');
      const firstLoadStart = performance.now();
      
      await fontEmbedder.embedFont('Dancing Script', 400);
      
      const firstLoadTime = performance.now() - firstLoadStart;
      addLog(`⏱️ 首次加载时间: ${Math.round(firstLoadTime)}ms`);
      
      // 测试缓存加载
      addLog('🔄 测试缓存加载...');
      const cachedLoadStart = performance.now();
      
      await fontEmbedder.embedFont('Dancing Script', 400);
      
      const cachedLoadTime = performance.now() - cachedLoadStart;
      addLog(`⚡ 缓存加载时间: ${Math.round(cachedLoadTime)}ms`);
      
      const speedup = firstLoadTime / cachedLoadTime;
      addLog(`📈 性能提升: ${speedup.toFixed(1)}x`);
      
      updateCacheStats();
      
    } catch (error) {
      addLog(`❌ 性能测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testMultipleFonts = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🚀 开始多字体加载测试...');
      
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);
      
      const fonts = [
        { family: 'Dancing Script', weight: 400 },
        { family: 'Dancing Script', weight: 500 },
        { family: 'Dancing Script', weight: 600 },
        { family: 'Dancing Script', weight: 700 }
      ];
      
      addLog(`📝 测试加载 ${fonts.length} 个字体...`);
      const totalStart = performance.now();
      
      for (const font of fonts) {
        const start = performance.now();
        const result = await fontEmbedder.embedFont(font.family, font.weight);
        const time = performance.now() - start;
        
        if (result.success) {
          addLog(`✅ ${font.family} ${font.weight}: ${Math.round(time)}ms (${result.source})`);
        } else {
          addLog(`❌ ${font.family} ${font.weight}: 加载失败`);
        }
      }
      
      const totalTime = performance.now() - totalStart;
      addLog(`🎉 总加载时间: ${Math.round(totalTime)}ms`);
      addLog(`📊 平均每字体: ${Math.round(totalTime / fonts.length)}ms`);
      
      updateCacheStats();
      
    } catch (error) {
      addLog(`❌ 多字体测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const clearCache = () => {
    const cacheManager = FontCacheManager.getInstance();
    cacheManager.clear();
    updateCacheStats();
    addLog('🗑️ 字体缓存已清空');
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">字体缓存和性能测试</h1>
      
      {/* 缓存统计 */}
      {cacheStats && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">缓存统计</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium">缓存字体数</div>
              <div className="text-lg">{cacheStats.totalFonts}</div>
            </div>
            <div>
              <div className="font-medium">缓存大小</div>
              <div className="text-lg">{Math.round(cacheStats.totalSize / 1024)} KB</div>
            </div>
            <div>
              <div className="font-medium">命中率</div>
              <div className="text-lg">{(cacheStats.hitRate * 100).toFixed(1)}%</div>
            </div>
            <div>
              <div className="font-medium">命中/未命中</div>
              <div className="text-lg">{cacheStats.hits}/{cacheStats.misses}</div>
            </div>
          </div>
        </div>
      )}
      
      <div className="space-y-4 mb-6">
        <button
          onClick={testFontPreloading}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {isLoading ? '测试中...' : '测试字体预加载'}
        </button>
        
        <button
          onClick={testCachePerformance}
          disabled={isLoading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 ml-4"
        >
          {isLoading ? '测试中...' : '测试缓存性能'}
        </button>
        
        <button
          onClick={testMultipleFonts}
          disabled={isLoading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 ml-4"
        >
          {isLoading ? '测试中...' : '测试多字体加载'}
        </button>
        
        <button
          onClick={clearCache}
          disabled={isLoading}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50 ml-4"
        >
          清空缓存
        </button>
        
        <button
          onClick={updateCacheStats}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 ml-4"
        >
          刷新统计
        </button>
      </div>
      
      <div className="bg-gray-100 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">测试日志</h2>
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {logs.map((log, index) => (
            <div key={index} className="text-sm font-mono">
              {log}
            </div>
          ))}
        </div>
      </div>
      
      <div className="mt-6 p-4 bg-green-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">字体缓存优化特性</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>✅ 智能缓存管理，避免重复下载字体文件</li>
          <li>✅ 自动过期清理，防止缓存无限增长</li>
          <li>✅ 本地存储持久化，跨会话保持缓存</li>
          <li>✅ 性能统计监控，实时了解缓存效果</li>
          <li>✅ 字体预加载，提升首次使用体验</li>
          <li>✅ 内存限制保护，防止内存溢出</li>
        </ul>
      </div>
    </div>
  );
}
