'use client';

import { useState } from 'react';
import { PDFDocument } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { UnifiedFontEmbedder } from '@/lib/unified-font-embedder';
import { FontFallbackManager } from '@/lib/font-fallback-manager';

export default function TestFontFallbackPage() {
  const [logs, setLogs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [fallbackResults, setFallbackResults] = useState<any>(null);

  const addLog = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testFallbackMechanism = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🚀 开始字体降级机制测试...');
      
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);
      
      // 测试正常字体加载
      addLog('📝 测试正常字体加载...');
      const normalResult = await fontEmbedder.embedFontWithFallback('Dancing Script', 400);
      
      if (normalResult.success) {
        addLog(`✅ 正常加载成功: ${normalResult.fontName} (${normalResult.source})`);
      } else {
        addLog(`❌ 正常加载失败`);
      }
      
      // 测试不存在的字体（触发降级）
      addLog('📝 测试不存在的字体（触发降级）...');
      const fallbackResult = await fontEmbedder.embedFontWithFallback('NonExistentFont', 400);
      
      if (fallbackResult.success) {
        addLog(`✅ 降级成功: ${fallbackResult.fontName} (${fallbackResult.source})`);
      } else {
        addLog(`❌ 降级失败`);
      }
      
      // 测试各种权重的降级
      const weights = [400, 500, 600, 700];
      for (const weight of weights) {
        addLog(`📝 测试 Dancing Script ${weight} 降级...`);
        const result = await fontEmbedder.embedFontWithFallback('Dancing Script', weight);
        
        if (result.success) {
          addLog(`✅ Dancing Script ${weight}: ${result.fontName} (${result.source})`);
        } else {
          addLog(`❌ Dancing Script ${weight}: 加载失败`);
        }
      }
      
      addLog('🎉 字体降级机制测试完成！');
      
    } catch (error) {
      addLog(`❌ 测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testFallbackStrategies = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🚀 开始降级策略测试...');
      
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      const fallbackManager = new FontFallbackManager(pdfDoc);
      
      const fontsToTest = [
        'Dancing Script',
        'Great Vibes',
        'Playfair Display',
        'Inter',
        'NonExistentFont'
      ];
      
      const results: any = {};
      
      for (const family of fontsToTest) {
        addLog(`📝 测试 ${family} 的降级策略...`);
        
        try {
          const strategyTest = await fallbackManager.testFallbackStrategy(family, 400);
          results[family] = strategyTest;
          
          addLog(`📋 ${family} 降级策略包含 ${strategyTest.strategy.fallbacks.length} 个选项:`);
          
          strategyTest.results.forEach((result, index) => {
            const fallback = result.fallback;
            const status = result.success ? '✅' : '❌';
            const type = fallback.type;
            const name = fallback.family || fallback.standardFont;
            
            addLog(`   ${status} ${index + 1}. ${type}: ${name}`);
            
            if (result.error) {
              addLog(`      错误: ${result.error}`);
            }
          });
          
        } catch (error) {
          addLog(`❌ ${family} 策略测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }
      
      setFallbackResults(results);
      addLog('🎉 降级策略测试完成！');
      
    } catch (error) {
      addLog(`❌ 策略测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testErrorRecovery = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🚀 开始错误恢复测试...');
      
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);
      
      // 模拟网络错误（使用无效URL）
      addLog('📝 模拟网络错误场景...');
      
      // 测试多个可能失败的字体
      const problematicFonts = [
        'InvalidFont1',
        'InvalidFont2',
        'InvalidFont3'
      ];
      
      for (const font of problematicFonts) {
        addLog(`📝 测试错误恢复: ${font}...`);
        
        const result = await fontEmbedder.embedFontWithFallback(font, 400);
        
        if (result.success) {
          addLog(`✅ 错误恢复成功: ${font} -> ${result.fontName}`);
        } else {
          addLog(`❌ 错误恢复失败: ${font}`);
        }
      }
      
      // 测试PDF生成是否仍然可以工作
      addLog('📝 测试PDF生成是否正常工作...');
      
      const page = pdfDoc.addPage([600, 400]);
      const result = await fontEmbedder.embedFontWithFallback('Dancing Script', 400);
      
      if (result.success) {
        page.drawText('字体降级测试 - Font Fallback Test', {
          x: 50,
          y: 300,
          size: 24,
          font: result.font
        });
        
        const pdfBytes = await pdfDoc.save();
        addLog(`✅ PDF生成成功 (${Math.round(pdfBytes.length / 1024)} KB)`);
        
        // 下载测试PDF
        const blob = new Blob([pdfBytes], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'font-fallback-test.pdf';
        link.click();
        URL.revokeObjectURL(url);
        
        addLog('💾 测试PDF下载完成');
      } else {
        addLog('❌ PDF生成失败');
      }
      
      addLog('🎉 错误恢复测试完成！');
      
    } catch (error) {
      addLog(`❌ 错误恢复测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">字体降级和错误处理测试</h1>
      
      <div className="space-y-4 mb-6">
        <button
          onClick={testFallbackMechanism}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {isLoading ? '测试中...' : '测试降级机制'}
        </button>
        
        <button
          onClick={testFallbackStrategies}
          disabled={isLoading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 ml-4"
        >
          {isLoading ? '测试中...' : '测试降级策略'}
        </button>
        
        <button
          onClick={testErrorRecovery}
          disabled={isLoading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 ml-4"
        >
          {isLoading ? '测试中...' : '测试错误恢复'}
        </button>
      </div>
      
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-4">测试日志</h2>
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {logs.map((log, index) => (
            <div key={index} className="text-sm font-mono">
              {log}
            </div>
          ))}
        </div>
      </div>
      
      {fallbackResults && (
        <div className="bg-white p-4 rounded-lg border mb-6">
          <h2 className="text-xl font-semibold mb-4">降级策略详情</h2>
          <div className="space-y-4">
            {Object.entries(fallbackResults).map(([family, data]: [string, any]) => (
              <div key={family} className="border rounded p-3">
                <h3 className="font-medium text-lg mb-2">{family}</h3>
                <div className="text-sm space-y-1">
                  {data.results.map((result: any, index: number) => (
                    <div key={index} className={`flex items-center ${result.success ? 'text-green-600' : 'text-red-600'}`}>
                      <span className="mr-2">{result.success ? '✅' : '❌'}</span>
                      <span>{result.fallback.type}: {result.fallback.family || result.fallback.standardFont}</span>
                      {result.error && <span className="ml-2 text-gray-500">({result.error})</span>}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <div className="p-4 bg-green-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">字体降级和错误处理特性</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>✅ 多层次降级策略，确保字体始终可用</li>
          <li>✅ 智能权重匹配，选择最接近的字体权重</li>
          <li>✅ 优雅错误处理，避免PDF生成失败</li>
          <li>✅ 自动标准字体后备，保证基本功能</li>
          <li>✅ 详细的降级日志，便于问题诊断</li>
          <li>✅ 可配置的降级策略，支持自定义</li>
        </ul>
      </div>
    </div>
  );
}
