# Certificate Maker 文档

欢迎来到 Certificate Maker 的完整文档。这里包含了项目的详细技术文档、使用指南和开发说明。

## 📚 文档目录

### 核心文档
- [项目概述](./overview.md) - 项目功能、架构和技术栈概览
- [安装配置指南](./installation.md) - 环境要求、安装步骤和配置说明
- [API文档](./api.md) - 核心类和方法的详细说明

### 字体系统文档
- [字体管理系统](./font-system.md) - 统一字体嵌入器和管理系统
- [Dancing Script解决方案](./dancing-script-solution.md) - 字体显示问题的完整解决方案
- [字体缓存和性能](./font-performance.md) - 缓存机制和性能优化

### 使用指南
- [基础使用教程](./usage-guide.md) - 如何使用证书生成器
- [模板开发指南](./template-development.md) - 如何创建和自定义模板
- [PDF生成指南](./pdf-generation.md) - 客户端和服务器端PDF生成

### 测试和质量保证
- [测试指南](./testing.md) - 如何运行测试和测试覆盖范围
- [故障排除](./troubleshooting.md) - 常见问题和解决方案
- [性能优化](./performance.md) - 性能优化策略和最佳实践

### 开发文档
- [开发指南](./development.md) - 开发环境设置和开发流程
- [贡献指南](./contributing.md) - 如何为项目做贡献
- [部署指南](./deployment.md) - 生产环境部署说明

## 🚀 快速导航

### 新用户
如果你是第一次使用 Certificate Maker，建议按以下顺序阅读：
1. [项目概述](./overview.md)
2. [安装配置指南](./installation.md)
3. [基础使用教程](./usage-guide.md)

### 开发者
如果你想参与开发或自定义功能：
1. [开发指南](./development.md)
2. [API文档](./api.md)
3. [字体管理系统](./font-system.md)
4. [测试指南](./testing.md)

### 问题解决
遇到问题时的查找顺序：
1. [故障排除](./troubleshooting.md)
2. [测试指南](./testing.md)
3. [GitHub Issues](https://github.com/your-repo/issues)

## 📝 文档维护

本文档会随着项目的发展持续更新。如果你发现文档中的错误或需要补充的内容，欢迎：

1. 提交 Issue 报告问题
2. 提交 Pull Request 改进文档
3. 联系维护团队

## 🔗 相关链接

- [项目仓库](https://github.com/your-repo)
- [在线演示](https://your-demo-url.com)
- [问题反馈](https://github.com/your-repo/issues)
- [讨论区](https://github.com/your-repo/discussions)

---

最后更新：2024年1月
维护者：Certificate Maker 团队
