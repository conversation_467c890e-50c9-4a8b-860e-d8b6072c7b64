/**
 * 服务器端PDF生成客户端
 * 调用服务器端API生成高质量PDF
 */

import { CertificateTemplate, CertificateData } from '@/types/certificate';

export interface ServerPDFGenerationResult {
  success: boolean;
  pdfBlob?: Blob;
  error?: string;
}

/**
 * 使用服务器端API生成PDF
 */
export async function generatePDFOnServer(
  template: CertificateTemplate,
  data: CertificateData
): Promise<ServerPDFGenerationResult> {
  try {
    console.log('🌐 Calling server-side PDF generation API...');
    
    const response = await fetch('/api/generate-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        templateId: template.id,
        data: data
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const pdfBlob = await response.blob();
    
    console.log(`✅ Server PDF generated successfully: ${Math.round(pdfBlob.size / 1024)} KB`);
    
    return {
      success: true,
      pdfBlob
    };

  } catch (error) {
    console.error('❌ Server PDF generation failed:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 下载服务器生成的PDF
 */
export async function downloadServerPDF(
  template: CertificateTemplate,
  data: CertificateData,
  filename?: string
): Promise<boolean> {
  try {
    const result = await generatePDFOnServer(template, data);
    
    if (!result.success || !result.pdfBlob) {
      throw new Error(result.error || 'PDF generation failed');
    }

    // 创建下载链接
    const url = URL.createObjectURL(result.pdfBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `certificate-${data.recipientName}-${Date.now()}.pdf`;
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 清理URL
    URL.revokeObjectURL(url);
    
    console.log('💾 Server PDF downloaded successfully');
    return true;

  } catch (error) {
    console.error('❌ Server PDF download failed:', error);
    return false;
  }
}

/**
 * 比较客户端和服务器端PDF生成
 */
export async function comparePDFGeneration(
  template: CertificateTemplate,
  data: CertificateData
): Promise<{
  clientSuccess: boolean;
  serverSuccess: boolean;
  clientSize?: number;
  serverSize?: number;
  clientTime?: number;
  serverTime?: number;
}> {
  const results = {
    clientSuccess: false,
    serverSuccess: false,
    clientSize: undefined as number | undefined,
    serverSize: undefined as number | undefined,
    clientTime: undefined as number | undefined,
    serverTime: undefined as number | undefined
  };

  // 测试客户端生成
  try {
    console.log('🔄 Testing client-side PDF generation...');
    const clientStart = performance.now();
    
    // 这里需要导入客户端PDF生成器
    // const clientPDF = await generateClientPDF(template, data);
    // results.clientSuccess = true;
    // results.clientSize = clientPDF.length;
    // results.clientTime = performance.now() - clientStart;
    
    console.log('⚠️ Client-side test skipped (not implemented in this function)');
  } catch (error) {
    console.error('❌ Client-side PDF generation failed:', error);
  }

  // 测试服务器端生成
  try {
    console.log('🌐 Testing server-side PDF generation...');
    const serverStart = performance.now();
    
    const serverResult = await generatePDFOnServer(template, data);
    results.serverSuccess = serverResult.success;
    
    if (serverResult.pdfBlob) {
      results.serverSize = serverResult.pdfBlob.size;
    }
    
    results.serverTime = performance.now() - serverStart;
    
  } catch (error) {
    console.error('❌ Server-side PDF generation failed:', error);
  }

  return results;
}
