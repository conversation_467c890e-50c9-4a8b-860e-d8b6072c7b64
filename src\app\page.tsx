import { Metadata } from 'next';
import HomePage from '@/components/pages/HomePage';

export const metadata: Metadata = {
  title: 'Free Certificate Maker | Create Professional PDF Certificates Online',
  description: 'Create professional certificates online for free with our certificate maker. Choose from premium certificate templates, customize with your details, and download high-quality PDF certificates instantly. No registration required.',

  authors: [{ name: 'Certificate Maker Team' }],
  creator: 'Certificate Maker',
  publisher: 'Certificate Maker',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://certificatemaker.app'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'Free Certificate Maker | Create Professional PDF Certificates Online',
    description: 'Create professional certificates online for free with our certificate maker. Choose from premium certificate templates, customize with your details, and download high-quality PDF certificates instantly.',
    url: 'https://certificatemaker.app',
    siteName: 'Certificate Maker',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Free Certificate Maker - Create Professional PDF Certificates Online',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Free Certificate Maker | Create Professional PDF Certificates Online',
    description: 'Create professional certificates online for free with our certificate maker. Choose from premium certificate templates and download high-quality PDF certificates instantly.',
    images: ['/og-image.jpg'],
    creator: '@certificatemaker',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function Home() {
  return <HomePage />;
}
