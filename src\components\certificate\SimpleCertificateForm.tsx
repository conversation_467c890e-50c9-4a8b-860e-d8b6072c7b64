'use client';

import React from 'react';
import { CertificateData, CertificateTemplate, CertificateCategory } from '@/types/certificate';

interface SimpleCertificateFormProps {
  template: CertificateTemplate;
  formData: CertificateData;
  onFormDataChange: (data: CertificateData) => void;
}

export default function SimpleCertificateForm({
  template,
  formData,
  onFormDataChange,
}: SimpleCertificateFormProps) {
  const handleInputChange = (field: keyof CertificateData, value: string) => {
    onFormDataChange({
      ...formData,
      [field]: value,
    });
  };

  // 根据证书类别生成动态占位符
  const getDetailsPlaceholder = (category: CertificateCategory): string => {
    const placeholders = {
      [CertificateCategory.ACHIEVEMENT]: "Enter the achievement details (e.g., 'for outstanding performance in Advanced Web Development and demonstrating exceptional skills in React, TypeScript, and modern web technologies')",
      [CertificateCategory.COMPLETION]: "Enter the course or program completed (e.g., 'for successfully completing the Full Stack Web Development Bootcamp with excellent grades and practical project implementation')",
      [CertificateCategory.PARTICIPATION]: "Enter the event or activity participated in (e.g., 'for active participation in the Annual Tech Conference 2024 and contributing to meaningful discussions on emerging technologies')",
      [CertificateCategory.EXCELLENCE]: "Enter the area of excellence (e.g., 'for demonstrating excellence in leadership, innovation, and outstanding contribution to the software development team')",
      [CertificateCategory.CUSTOM]: "Enter the certificate details (e.g., 'for meeting the requirements and demonstrating proficiency in the specified area of expertise')"
    };

    return placeholders[category] || placeholders[CertificateCategory.CUSTOM];
  };

  const detailsPlaceholder = getDetailsPlaceholder(template.category);

  return (
    <div className="space-y-6">
      {/* Name Field */}
      <div>
        <label htmlFor="name" className="block text-sm font-semibold text-gray-800 mb-3">
          Recipient Name *
        </label>
        <textarea
          id="name"
          placeholder="Enter the name of the certificate recipient"
          value={formData.recipientName}
          onChange={(e) => handleInputChange('recipientName', e.target.value)}
          maxLength={template.constraints.nameMaxLength}
          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-base transition-all duration-200 hover:border-gray-300"
          rows={2}
          required
        />
        <p className="text-xs text-gray-500 mt-1">
          Maximum {template.constraints.nameMaxLength} characters
        </p>
      </div>

      {/* Details Field */}
      <div>
        <label htmlFor="subtitle" className="block text-sm font-semibold text-gray-800 mb-3">
          Certificate Details *
        </label>
        <textarea
          id="subtitle"
          placeholder={detailsPlaceholder}
          value={formData.details}
          onChange={(e) => handleInputChange('details', e.target.value)}
          maxLength={template.constraints.detailsMaxLength}
          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-base transition-all duration-200 hover:border-gray-300"
          rows={4}
          required
        />
        <p className="text-xs text-gray-500 mt-1">
          Maximum {template.constraints.detailsMaxLength} characters
        </p>
      </div>

      {/* Date and Signature in a row for better layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Date Field */}
        <div>
          <label htmlFor="date" className="block text-sm font-semibold text-gray-800 mb-3">
            Date *
          </label>
          <textarea
            id="date"
            placeholder="Enter the date (e.g., 'January 15, 2024')"
            value={formData.date}
            onChange={(e) => handleInputChange('date', e.target.value)}
            maxLength={template.constraints.dateMaxLength}
            className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-base transition-all duration-200 hover:border-gray-300"
            rows={2}
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            Maximum {template.constraints.dateMaxLength} characters
          </p>
        </div>

        {/* Signature Field */}
        <div>
          <label htmlFor="signature" className="block text-sm font-semibold text-gray-800 mb-3">
            Signature *
          </label>
          <textarea
            id="signature"
            placeholder="Enter the name of the person signing (e.g., 'John Smith, Director')"
            value={formData.signature}
            onChange={(e) => handleInputChange('signature', e.target.value)}
            maxLength={template.constraints.signatureMaxLength}
            className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-base transition-all duration-200 hover:border-gray-300"
            rows={2}
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            Maximum {template.constraints.signatureMaxLength} characters
          </p>
        </div>
      </div>

    </div>
  );
}
